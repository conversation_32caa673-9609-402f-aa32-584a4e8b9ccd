# Server Configuration
PORT=3006
NODE_ENV=development

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=atma_password
DB_SCHEMA=chat
DB_POOL_MAX=20
DB_POOL_MIN=5

# Security
JWT_SECRET=atma_secure_jwt_secret_key
INTERNAL_SERVICE_KEY=internal_service_secret_key

# Rate Limiting
RATE_LIMIT_CONVERSATIONS_PER_DAY=100
MAX_MESSAGE_LENGTH=4000

# CORS Configuration
ALLOWED_ORIGINS=*

# Monitoring
LOG_LEVEL=info
LOG_FILE=logs/chatbot-service.log

# Service URLs (for future integration)
AUTH_SERVICE_URL=http://auth-service:3001
