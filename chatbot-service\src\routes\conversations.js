const express = require('express');
const conversationController = require('../controllers/conversationController');
const { validateBody, validateQuery, schemas } = require('../middleware/validation');
// const authMiddleware = require('../middleware/auth'); // Will be implemented in next task

const router = express.Router();

// Temporary middleware to simulate authenticated user (will be replaced with real auth)
const tempAuthMiddleware = (req, res, next) => {
  // This is a placeholder - will be replaced with real JWT auth middleware
  req.user = {
    id: '550e8400-e29b-41d4-a716-446655440000' // Placeholder user ID
  };
  next();
};

// Routes
router.post('/',
  tempAuthMiddleware,
  validateBody(schemas.createConversation),
  conversationController.createConversation
);

router.get('/',
  tempAuthMiddleware,
  validateQuery(schemas.getConversationsQuery),
  conversationController.getConversations
);

router.get('/:id',
  tempAuthMiddleware,
  validateQuery(schemas.getConversationQuery),
  conversationController.getConversation
);

router.put('/:id',
  tempAuthMiddleware,
  validateBody(schemas.updateConversation),
  conversationController.updateConversation
);

router.delete('/:id',
  tempAuthMiddleware,
  conversationController.deleteConversation
);

router.get('/:id/messages',
  tempAuthMiddleware,
  validateQuery(schemas.getMessagesQuery),
  conversationController.getConversationMessages
);

module.exports = router;
